defmodule Repobot.Folders do
  @moduledoc """
  The Folders context.
  """

  alias Repobot.Folder
  alias Repobot.Folders.Commands

  import Ecto.Query, warn: false

  alias Repobot.Repo
  alias Repobot.{Folder, Repository}

  @doc """
  Returns the list of folders for a user.
  If search is provided, filters repositories in each folder by the search term.
  """
  def list_folders(user, search \\ nil, organization_id \\ nil)

  def list_folders(user, nil, organization_id) do
    Folder
    |> where([f], f.organization_id == ^(organization_id || user.default_organization_id))
    |> order_by([f], desc: f.starred, asc: f.name)
    |> preload([:repositories, :template_repositories, :source_files])
    |> Repo.all()
  end

  def list_folders(user, search, organization_id) when is_binary(search) and search != "" do
    search = "%#{search}%"

    # Create a subquery that combines both regular and template repositories
    repository_query =
      from r in Repository,
        where: ilike(r.full_name, ^search)

    Folder
    |> where([f], f.organization_id == ^(organization_id || user.default_organization_id))
    |> order_by([f], desc: f.starred, asc: f.name)
    |> preload(repositories: ^repository_query)
    |> preload(template_repositories: ^repository_query)
    |> Repo.all()
  end

  def list_folders(user, _search, organization_id), do: list_folders(user, nil, organization_id)

  @doc """
  Gets a single folder.
  Raises `Ecto.NoResultsError` if the Folder does not exist.
  """
  def get_folder!(id) do
    Folder
    |> Repo.get!(id)
    |> Repo.preload([
      :source_files,
      repositories: [:source_files],
      template_repositories: [:source_files]
    ])
  end

  @doc """
  Creates a folder.
  """
  def create_folder(params \\ %{}) do
    Commands.Save.call(%{action: :new, params: params})
  end

  @doc """
  Updates a folder.
  """
  def update_folder(%Folder{} = folder, params) do
    Commands.Save.call(%{action: :edit, folder: folder, params: params})
  end

  @doc """
  Deletes a folder.
  """
  def delete_folder(%Folder{} = folder) do
    # First, unset folder_id for all repositories in this folder
    from(r in Repository, where: r.folder_id == ^folder.id)
    |> Repo.update_all(set: [folder_id: nil])

    Repo.delete(folder)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking folder changes.
  """
  def change_folder(%Folder{} = folder, attrs \\ %{}) do
    Folder.changeset(folder, attrs)
  end

  @doc """
  Adds a repository to a folder.
  """
  def add_repository_to_folder(%Repository{} = repository, %Folder{} = folder) do
    repository
    |> Repository.changeset(%{folder_id: folder.id})
    |> Repo.update()
  end

  @doc """
  Removes a repository from its folder.
  """
  def remove_repository_from_folder(%Repository{} = repository) do
    repository
    |> Repository.changeset(%{folder_id: nil})
    |> Repo.update()
  end

  @doc """
  Toggles the starred status of a folder.
  """
  def toggle_starred(%Folder{} = folder) do
    folder
    |> Folder.changeset(%{starred: !folder.starred})
    |> Repo.update()
  end
end
