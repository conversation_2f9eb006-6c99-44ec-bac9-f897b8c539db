defmodule Repobot.Folders.Commands do
  alias <PERSON>obot.Folder

  defmodule Save do
    use Repobot.Commands.Save, type: :form

    schema(Folder, accept: [:name, :organization_id, :settings], default_presence: :optional)

    steps do
      @impl true
      def prepare(%{params: %{settings: settings} = params} = context) do
        {:ok, %{context | params: %{params | settings: JSON.decode!(settings)}}}
      end

      def prepare(context), do: super(context)
    end

    @impl true
    def get_struct(%{folder: folder}), do: folder
    def get_struct(context), do: super(context)

    @impl true
    def validate_changeset(%{changeset: changeset}) do
      changeset
      |> validate_required([:name, :organization_id])
      |> validate_length(:name, min: 3, max: 255)
      |> unique_constraint([:name, :organization_id])
    end
  end
end
