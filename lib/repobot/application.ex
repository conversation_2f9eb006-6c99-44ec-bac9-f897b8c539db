defmodule Repobot.Application do
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications
  @moduledoc false

  use Application

  @impl true
  def start(_type, _args) do
    :ok = Application.ensure_started(:inets)

    :logger.add_handler(:my_sentry_handler, Sentry.LoggerHandler, %{
      config: %{
        metadata: [
          :file,
          :line,
          :event,
          :user_id,
          :workspace_id,
          :network,
          :post_id,
          :content_id,
          :reason,
          :result
        ],
        capture_log_messages: true,
        level: :error
      }
    })

    # if System.get_env("SENTRY_DSN") do
    #   opt_ins = [
    #     OpenTelemetry.SemConv.ClientAttributes.client_port(),
    #     OpenTelemetry.SemConv.NetworkAttributes.network_local_address(),
    #     OpenTelemetry.SemConv.NetworkAttributes.network_local_port(),
    #     OpenTelemetry.SemConv.NetworkAttributes.network_transport()
    #   ]

    #   OpentelemetryBandit.setup(opt_in_attrs: opt_ins)

    #   OpentelemetryPhoenix.setup(adapter: :bandit, endpoint_prefix: [RepobotWeb.Endpoint])
    #   OpentelemetryEcto.setup([:repobot, :repo])
    # end

    children = [
      RepobotWeb.Telemetry,
      Repobot.Repo,
      {DNSCluster, query: Application.get_env(:repobot, :dns_cluster_query) || :ignore},
      {Phoenix.PubSub, name: Repobot.PubSub},
      {Finch, name: Repobot.Finch},
      {Oban, Application.fetch_env!(:repobot, Oban)},
      Repobot.Vault,
      RepobotWeb.Endpoint
    ]

    opts = [strategy: :one_for_one, name: Repobot.Supervisor]
    pid = Supervisor.start_link(children, opts)

    Drops.Relation.Cache.warm_up(Repobot.Repo, ["folders"])

    pid
  end

  @impl true
  def config_change(changed, _new, removed) do
    RepobotWeb.Endpoint.config_change(changed, removed)
    :ok
  end
end
