defmodule Repobot do
  @moduledoc """
  Repobot keeps the contexts that define your domain
  and business logic.

  Contexts are also responsible for managing your data, regardless
  if it comes from the database, an external API or others.
  """
  defmodule Command do
    use Drops.Operations.Command, repo: Repobot.Repo, logging: true
  end

  defmodule Form do
    use Command, type: :form
  end

  defmodule Commands.Save do
    use Repobot.Command

    steps do
      @impl true
      def execute(%{action: :new, changeset: changeset}), do: insert(changeset)
      def execute(%{action: :edit, changeset: changeset}), do: update(changeset)
    end
  end
end
