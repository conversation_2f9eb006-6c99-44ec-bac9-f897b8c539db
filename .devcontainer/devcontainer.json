{"name": "repobot", "dockerComposeFile": "../docker-compose.yml", "service": "dev", "runServices": ["dev"], "workspaceFolder": "/workspace/repobot", "postCreateCommand": "apt update && apt install -y zsh neovim less", "customizations": {"vscode": {"extensions": ["sleistner.vscode-fileutils", "kahole.magit", "GitHub.vscode-pull-request-github", "JakeBecker.elixir-ls"], "settings": {"terminal.integrated.shell.linux": "/usr/local/bin/zsh", "editor.formatOnSave": true}}}}